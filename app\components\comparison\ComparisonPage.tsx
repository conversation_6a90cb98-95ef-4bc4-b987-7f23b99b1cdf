import <PERSON><PERSON>William from "../william/CTAWilliam";
import ComparisonHero from "./ComparisonHero";
import ProblemSection from "./ProblemSection";
import ComparisonTable from "./ComparisonTable";
import PricingComparison from "./PricingComparison";
import FeatureComparison from "./FeatureComparison";
import TestimonialsWithCTA from "./TestimonialsWithCTA";
import FAQWithCTA from "./FAQWithCTA";

export interface ComparisonData {
  metadata: {
    title: string;
    description: string;
    canonical?: boolean;
    competitor: string;
  };
  heroSection: {
    headline: string;
    subheadline: string;
    description: string;
    featuresHighlight: string[];
  };
  problemSection: {
    painPoints: string;
    commonChallenges: string[];
    painPointsClosing: string;
    solutionIntroduction: string;
    solutionDescription: string;
    keyFeatures: Array<{
      name: string;
      description: string;
    }>;
  };
  propositionSection: {
    headline: string;
    comparisonTable: Array<{
      feature: string;
      inloop: string;
      competitor: string;
    }>;
  };
  pricingComparisonSection: any;
  featureByFeatureSection: any;
  faqSection: {
    faqs: Array<{
      question: string;
      answer: string;
    }>;
  };
  ctaSections: {
    pricing: any;
    faq: any;
  };
}

interface ComparisonPageProps {
  data: ComparisonData;
}

export default function ComparisonPage({ data }: ComparisonPageProps) {
  return (
    <>
      <ComparisonHero {...data.heroSection} />

      <ProblemSection {...data.problemSection} />

      <ComparisonTable
        {...data.propositionSection}
        competitorName={data.metadata.competitor}
      />

      <PricingComparison
        {...data.pricingComparisonSection}
        competitorName={data.metadata.competitor}
        ctaContent={data.ctaSections.pricing}
      />

      <FeatureComparison
        {...data.featureByFeatureSection}
        competitorName={data.metadata.competitor}
      />

      <TestimonialsWithCTA />

      <FAQWithCTA
        competitorName={data.metadata.competitor}
        faqs={data.faqSection.faqs}
        ctaContent={data.ctaSections.faq}
      />

      <CTAWilliam
        title={`Ready to upgrade from ${data.metadata.competitor}?`}
      />
    </>
  );
}
